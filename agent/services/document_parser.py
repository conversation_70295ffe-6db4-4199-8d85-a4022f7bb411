import PyPDF2
from docx import Document
import pandas as pd
from typing import Dict, List, Any
import re

class DocumentParser:
    def __init__(self):
        self.outline = None
        self.review_criteria = None
        self.standard_outline = {
            "1": "概述",
            "2": "项目建设背景和必要性",
            "3": "项目需求分析与预期产出",
            "4": "项目选址与要素保障",
            "5": "项目建设方案",
            "6": "项目运营方案",
            "7": "项目投融资与财务方案",
            "8": "项目影响效果分析",
            "9": "项目风险管控方案",
            "10": "研究结论及建议",
            "11": "附表"
        }
        # 目录相关关键词
        self.toc_keywords = [
            "目录", "目　录", "CONTENTS", "Contents", "contents",
            "TABLE OF CONTENTS", "Table of Contents"
        ]
        # 目录结束标识
        self.toc_end_keywords = [
            "摘要", "前言", "引言", "第一章", "1.", "1 ", "概述"
        ]

    def is_chapter_title(self, line: str) -> tuple[bool, str]:
        """判断是否为章节标题"""
        line = line.strip()

        # 匹配数字编号的章节标题（更灵活的模式）
        chapter_patterns = [
            r'^(\d+)[.、\s]*(.+)$',  # 1概述, 1. 概述, 1 概述
            r'^第(\d+)章[.、\s]*(.+)$',  # 第1章 概述
            r'^(\d+)[.、\s]*(.+?)\.{0,}$'  # 1概述...（去掉末尾的点）
        ]

        for pattern in chapter_patterns:
            match = re.match(pattern, line)
            if match:
                chapter_num = match.group(1)
                chapter_title = match.group(2).strip().rstrip('.')

                # 检查是否与标准大纲匹配（精确匹配或包含匹配）
                if chapter_num in self.standard_outline:
                    standard_title = self.standard_outline[chapter_num]
                    if (chapter_title == standard_title or
                        standard_title in chapter_title or
                        chapter_title in standard_title):
                        return True, f"{chapter_num} {standard_title}"

        # 匹配纯章节标题（不带编号）
        for num, title in self.standard_outline.items():
            if line == title or title in line:
                return True, f"{num} {title}"

        return False, ""

    def detect_toc_region(self, lines: List[str]) -> tuple[int, int]:
        """检测目录区域的开始和结束位置"""
        toc_start = -1
        toc_end = -1

        # 方法1: 查找明确的目录标识
        for i, line in enumerate(lines):
            line_clean = line.strip()
            if any(keyword in line_clean for keyword in self.toc_keywords):
                toc_start = i
                print(f"检测到目录开始位置: 第{i+1}行 - {line_clean}")
                break

        # 方法2: 如果没找到明确标识，检查前20行是否有目录特征
        if toc_start < 0:
            toc_lines_count = 0
            for i in range(min(20, len(lines))):
                line_clean = lines[i].strip()
                if self._is_toc_line(line_clean):
                    toc_lines_count += 1

            # 如果前20行中有超过5行是目录格式，认为是目录
            if toc_lines_count >= 5:
                toc_start = 0
                print(f"根据格式特征检测到目录开始: 第1行 (目录行数: {toc_lines_count})")

        # 查找目录结束位置
        if toc_start >= 0:
            # 从目录开始位置往后查找
            search_end = min(toc_start + 100, len(lines))  # 扩大搜索范围

            for i in range(toc_start + 1, search_end):
                line_clean = lines[i].strip()

                # 检查是否是目录结束标识
                if any(keyword in line_clean for keyword in self.toc_end_keywords):
                    toc_end = i
                    print(f"检测到目录结束位置: 第{i+1}行 - {line_clean}")
                    break

                # 检查是否是正文章节开始（带有实际内容的章节标题）
                is_title, chapter_title = self.is_chapter_title(line_clean)
                if is_title:
                    # 检查后续几行是否有实际内容（不是目录格式）
                    has_content = False
                    for j in range(i + 1, min(i + 15, len(lines))):
                        next_line = lines[j].strip()
                        if len(next_line) > 20 and not self._is_toc_line(next_line):
                            has_content = True
                            break

                    if has_content:
                        toc_end = i
                        print(f"检测到正文开始位置: 第{i+1}行 - {chapter_title}")
                        break

            # 如果还没找到结束位置，使用启发式方法
            if toc_end < 0:
                # 查找连续非目录行的开始
                non_toc_count = 0
                for i in range(toc_start + 1, search_end):
                    line_clean = lines[i].strip()
                    if not self._is_toc_line(line_clean) and len(line_clean) > 10:
                        non_toc_count += 1
                        if non_toc_count >= 3:  # 连续3行非目录行
                            toc_end = i - 2
                            print(f"启发式检测到目录结束位置: 第{toc_end+1}行")
                            break
                    else:
                        non_toc_count = 0

        return toc_start, toc_end

    def _is_toc_line(self, line: str) -> bool:
        """判断是否是目录行（包含页码、点号等目录特征）"""
        line = line.strip()

        if not line:
            return False

        # 目录行特征：
        # 1. 以数字结尾（页码）
        # 2. 包含连续的点号或破折号
        # 3. 章节标题+页码的格式
        # 4. 短行且包含章节关键词

        # 检查是否以页码结尾
        if re.search(r'\d+\s*$', line):
            return True

        # 检查是否包含目录格式的点号或省略号
        if re.search(r'\.{3,}', line) or re.search(r'…{2,}', line):
            return True

        # 检查章节编号+标题+页码的格式（如：1概述..............1）
        if re.match(r'^\d+[^\d]*\.{3,}\d+\s*$', line):
            return True

        # 检查是否是简短的章节标题行（目录中的标题通常较短且包含章节关键词）
        if len(line) < 80:
            # 检查是否包含章节编号
            if re.match(r'^\d+', line):
                # 检查是否包含标准章节关键词
                for title in self.standard_outline.values():
                    if title in line:
                        return True

            # 检查是否只包含章节关键词（无其他内容）
            for title in self.standard_outline.values():
                if line == title or line.startswith(title):
                    return True

        # 检查是否是纯数字行（页码）
        if line.isdigit() and len(line) <= 3:
            return True

        return False

    def parse_pdf(self, pdf_path: str) -> Dict[str, str]:
        """解析PDF文件，提取章节内容"""
        sections = {f"{num} {title}": "" for num, title in self.standard_outline.items()}
        current_section = None
        current_content = []
        all_text = ""

        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)

            # 先提取所有文本
            for page in reader.pages:
                try:
                    text = page.extract_text()
                    if text.strip():
                        all_text += text + "\n"
                except Exception as e:
                    print(f"页面文本提取失败: {e}")
                    continue

        if not all_text.strip():
            print("PDF文本提取失败，尝试其他方法...")
            # 如果PyPDF2失败，返回空内容但保持结构
            return sections

        lines = all_text.split('\n')
        print(f"提取到 {len(lines)} 行文本")

        # 检测并跳过目录区域
        toc_start, toc_end = self.detect_toc_region(lines)

        if toc_start >= 0 and toc_end >= 0:
            print(f"跳过目录区域: 第{toc_start+1}行到第{toc_end}行")
            # 创建跳过目录的行列表
            filtered_lines = lines[:toc_start] + lines[toc_end:]
        else:
            filtered_lines = lines
            print("未检测到目录区域，处理全部文本")

        # 改进的章节标题匹配
        in_content_area = False  # 标记是否已进入正文区域

        for i, line in enumerate(filtered_lines):
            line = line.strip()
            if not line:
                continue

            is_title, chapter_title = self.is_chapter_title(line)

            if is_title:
                # 验证这是否是真正的章节开始（不是目录中的标题）
                if self._validate_chapter_start(filtered_lines, i):
                    # 保存当前章节内容
                    if current_section and current_content:
                        sections[current_section] = '\n'.join(current_content).strip()
                    current_section = chapter_title
                    current_content = []
                    in_content_area = True
                    print(f"找到章节: {chapter_title}")
                else:
                    print(f"跳过疑似目录中的章节标题: {chapter_title}")
            elif current_section and in_content_area:
                # 过滤掉太短的行、页码、页眉页脚等
                if self._is_valid_content_line(line):
                    current_content.append(line)

        # 保存最后一个章节的内容
        if current_section and current_content:
            sections[current_section] = '\n'.join(current_content).strip()

        # 如果没有找到任何章节，尝试按页面分割内容
        if not any(content.strip() for content in sections.values()):
            print("未找到标准章节，尝试按内容关键词分割...")
            self._extract_by_keywords(all_text, sections)

        return sections

    def _validate_chapter_start(self, lines: List[str], index: int) -> bool:
        """验证是否是真正的章节开始"""
        # 检查后续几行是否有实际内容
        content_lines = 0
        for i in range(index + 1, min(index + 15, len(lines))):
            line = lines[i].strip()
            if self._is_valid_content_line(line):
                content_lines += 1
                if content_lines >= 2:  # 至少有2行有效内容
                    return True

        return False

    def _is_valid_content_line(self, line: str) -> bool:
        """判断是否是有效的内容行"""
        line = line.strip()

        # 过滤条件
        if len(line) < 5:  # 太短的行
            return False

        if line.isdigit():  # 纯数字（页码）
            return False

        if re.match(r'^[\d\-\s]+$', line):  # 只包含数字、横线、空格
            return False

        # 常见的页眉页脚模式
        footer_patterns = [
            r'^\d+$',  # 页码
            r'^第\s*\d+\s*页',  # 第X页
            r'^\d+\s*/\s*\d+$',  # X/Y页码格式
            r'^[\d\-]+$',  # 纯数字和横线
        ]

        for pattern in footer_patterns:
            if re.match(pattern, line):
                return False

        # 检查是否是目录行
        if self._is_toc_line(line):
            return False

        return True

    def _extract_by_keywords(self, text: str, sections: Dict[str, str]):
        """根据关键词提取章节内容"""
        keywords_map = {
            "1 概述": ["概述", "项目概况", "总体情况", "基本情况"],
            "2 项目建设背景和必要性": ["建设背景", "必要性", "项目背景", "建设必要性"],
            "3 项目需求分析与预期产出": ["需求分析", "预期产出", "需求", "产出"],
            "4 项目选址与要素保障": ["选址", "要素保障", "建设条件", "选址条件"],
            "5 项目建设方案": ["建设方案", "技术方案", "工程方案", "实施方案"],
            "6 项目运营方案": ["运营方案", "运营管理", "运行方案", "管理方案"],
            "7 项目投融资与财务方案": ["投融资", "财务方案", "投资估算", "资金筹措"],
            "8 项目影响效果分析": ["影响效果", "效益分析", "社会效益", "经济效益"],
            "9 项目风险管控方案": ["风险管控", "风险分析", "风险防范", "风险评估"],
            "10 研究结论及建议": ["研究结论", "建议", "结论", "总结"],
            "11 附表": ["附表", "附件", "附录", "表格"]
        }

        # 按段落分割文本，并尝试智能分配到章节
        lines = text.split('\n')
        current_paragraph = []

        for line in lines:
            line = line.strip()
            if not line:
                if current_paragraph:
                    paragraph_text = '\n'.join(current_paragraph)
                    self._assign_paragraph_to_section(paragraph_text, keywords_map, sections)
                    current_paragraph = []
            else:
                if self._is_valid_content_line(line):
                    current_paragraph.append(line)

        # 处理最后一个段落
        if current_paragraph:
            paragraph_text = '\n'.join(current_paragraph)
            self._assign_paragraph_to_section(paragraph_text, keywords_map, sections)

    def _assign_paragraph_to_section(self, paragraph: str, keywords_map: Dict[str, List[str]], sections: Dict[str, str]):
        """将段落分配到合适的章节"""
        if len(paragraph.strip()) < 30:  # 跳过太短的段落
            return

        # 首先检查段落开头是否有章节标识
        first_line = paragraph.split('\n')[0].strip()
        is_title, chapter_title = self.is_chapter_title(first_line)

        if is_title:
            # 如果段落以章节标题开始，将整个段落分配到该章节
            if sections[chapter_title]:
                sections[chapter_title] += "\n\n" + paragraph.strip()
            else:
                sections[chapter_title] = paragraph.strip()
            print(f"段落分配到 {chapter_title} (章节标题匹配)")
            return

        # 计算每个章节的匹配分数
        best_section = None
        best_score = 0

        for section_name, keywords in keywords_map.items():
            score = 0
            for keyword in keywords:
                # 关键词匹配计分
                count = paragraph.count(keyword)
                if count > 0:
                    score += len(keyword) * count  # 长关键词权重更高，出现次数也影响分数

            # 额外检查：如果段落包含章节编号
            section_num = section_name.split()[0]
            if f"{section_num}." in paragraph or f"{section_num} " in paragraph:
                score += 10  # 章节编号匹配给额外分数

            if score > best_score:
                best_score = score
                best_section = section_name

        # 如果有匹配的章节，分配段落
        if best_section and best_score > 0:
            if sections[best_section]:
                sections[best_section] += "\n\n" + paragraph.strip()
            else:
                sections[best_section] = paragraph.strip()
            print(f"段落分配到 {best_section} (匹配分数: {best_score})")
        else:
            # 如果没有明确匹配，尝试根据位置分配
            # 检查段落是否包含项目基本信息
            if any(keyword in paragraph.lower() for keyword in ['项目名称', '建设目标', '建设地点', '项目概况']):
                target_section = "1 概述"
            else:
                target_section = "1 概述"  # 默认分配到概述

            if sections[target_section]:
                sections[target_section] += "\n\n" + paragraph.strip()
            else:
                sections[target_section] = paragraph.strip()
            print(f"段落分配到默认章节: {target_section}")

    def parse_outline(self, word_path: str) -> Dict[str, Any]:
        """解析Word文档中的大纲"""
        doc = Document(word_path)
        outline = {}
        current_chapter = None

        for para in doc.paragraphs:
            text = para.text.strip()
            is_title, chapter_title = self.is_chapter_title(text)

            if is_title:
                current_chapter = chapter_title
                outline[current_chapter] = []
            elif current_chapter and text:
                outline[current_chapter].append(text)

        self.outline = outline
        return outline

    def parse_review_criteria(self, excel_path: str) -> List[Dict[str, Any]]:
        """解析Excel文件中的审查细则"""
        df = pd.read_excel(excel_path)
        criteria = []
        current_criterion_id = None
        criterion_counter = 0

        for index, row in df.iterrows():
            # 跳过完全空的行
            if (pd.isna(row.get('序号', '')) and
                pd.isna(row.get('审查细则', '')) and
                pd.isna(row.get('审查范畴', ''))):
                continue

            # 获取审查细则内容
            content = str(row.get('审查细则', '')).strip() if not pd.isna(row.get('审查细则', '')) else ''
            requirements = str(row.get('审查范畴', '')).strip() if not pd.isna(row.get('审查范畴', '')) else ''

            # 跳过没有实际内容的行
            if not content or content in ['nan', 'NaN', '']:
                continue

            # 处理序号列（可能是合并单元格）
            sequence_num = row.get('序号', '')
            if not pd.isna(sequence_num) and str(sequence_num).strip():
                # 如果序号不为空，更新当前审查细则ID
                current_criterion_id = str(sequence_num).strip()
                criterion_counter += 1
            else:
                # 如果序号为空（合并单元格），使用当前的审查细则ID
                # 如果还没有当前ID，使用行号
                if current_criterion_id is None:
                    criterion_counter += 1
                    current_criterion_id = str(criterion_counter)

            # 使用行号作为最终的criterion_id，确保唯一性
            final_criterion_id = f"{criterion_counter}.{index + 1}"

            criteria.append({
                'id': final_criterion_id,
                'original_id': current_criterion_id,
                'content': content,
                'requirements': requirements,
                'row_index': index + 1
            })

            print(f"解析审查细则 {len(criteria)}: ID={final_criterion_id}, 原始ID={current_criterion_id}, 内容={content[:30]}...")

        print(f"总共解析到 {len(criteria)} 个有效的审查细则")
        self.review_criteria = criteria
        return criteria