from services.document_parser import DocumentParser
import os

# 测试 PDF 解析
#pdf_path = "uploads/1.广西电网横州市可行性研究报告.pdf"
pdf_path = "uploads/test_可行性研究报告.pdf"

parser = DocumentParser()

print("测试 PDF 解析...")
try:
    sections = parser.parse_pdf(pdf_path)
    print(f"解析成功，提取到 {len(sections)} 个章节")

    for section_title, content in sections.items():
        print(f"\n章节: {section_title}")
        print(f"内容长度: {len(content)} 字符")
        if content.strip():
            print(f"内容预览: {content[:200]}...")
        else:
            print("内容为空")

except Exception as e:
    print(f"PDF 解析失败: {e}")
    import traceback
    traceback.print_exc()

